#pragma once

#include <CoreMinimal.h>
#include <Blueprint/UserWidget.h>

#include "DruidsSageChatTypes.h"
#include "IDruidsSageChatItem.h"

#include "DruidsSageAssistantChatItem.generated.h"

class UDruidsSageMessagingHandler;
class UTextBlock;
class UVerticalBox;
class USc<PERSON>Box;
class UDruidsSageSuggestedPromptsChatItem;
class ISuggestedPromptsHandler;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnActionAppliedAssistantUMG, const FString&, JsonString, UDruidsSageActionRequestChatItem*, ChatItem);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSuggestedPromptSelectedAssistantUMG, const FString&, PromptText, UDruidsSageSuggestedPromptsChatItem*, ChatItem);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnChatResponseCompleted);

UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsSageAssistantChatItem : public UIDruidsSageChatItem
{
	GENERATED_BODY()

public:
	UDruidsSageAssistantChatItem(const FObjectInitializer& ObjectInitializer);

	// UUserWidget interface
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual void SynchronizeProperties() override;
	// End of UUserWidget interface

	// UIDruidsSageChatItem interface implementation
	virtual FName GetTypeName() const override;
	virtual void FillInDruidsMessage(FDruidsSageChatMessage& Message) const override;
	virtual EDruidsSageChatRole GetMessageRole() const override;
	virtual TWeakObjectPtr<UDruidsSageMessagingHandler> GetMessagingHandler() const override;
	virtual void UpdateFromContentJson(const TSharedPtr<FJsonValue>& ContentJson) override;
	virtual FString GetPlainText() const override;

	// Initialization methods
	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void InitializeAssistantChatItem();

	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void SetScrollBoxReference(UScrollBox* InScrollBox);

	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void SetRawText(const FString& ContentText);

	/**
	 * Set the suggested prompts handler for processing fresh conversation prompts
	 * @param Handler The handler to use for processing suggested prompts
	 */
	void SetSuggestedPromptsHandler(TSharedPtr<ISuggestedPromptsHandler> Handler);

	/**
	 * Set whether this assistant chat item is the last one in the chat
	 * This affects whether suggested prompts should be included in chat history
	 * @param bIsLast True if this is the last assistant chat item
	 */
	void SetIsLastAssistantChatItem(bool bIsLast);

	// Notify all child chat items that a prompt message was sent
	void NotifyChildrenPromptMessageSent();

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Chat Item")
	FOnActionAppliedAssistantUMG OnActionApplied;

	UPROPERTY(BlueprintAssignable, Category = "Chat Item")
	FOnSuggestedPromptSelectedAssistantUMG OnSuggestedPromptSelected;

	UPROPERTY(BlueprintAssignable, Category = "Chat Item")
	FOnChatResponseCompleted OnChatResponseCompleted;

	static FName GetClassName() { return "UDruidsSageAssistantChatItem"; }

	UFUNCTION()
	void HandleActionResult(bool bActionCalled, bool bSuccess, const FString& Result, const FString& ErrorMessage);

protected:
	// BindWidget properties for Blueprint binding
	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UTextBlock* RoleWidget;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UVerticalBox* MessageContainer;

private:
	TWeakObjectPtr<UDruidsSageMessagingHandler> MessagingHandler;
	TSharedPtr<FJsonValue> PreviousContentJson;
	TArray<UIDruidsSageChatItem*> ChildChatItems;
	TWeakObjectPtr<class UDruidsSageActionRequestChatItem> CurrentActionRequestChatItem;
	TSharedPtr<ISuggestedPromptsHandler> SuggestedPromptsHandler;
	bool bIsLastAssistantChatItem = false;

	void SetupMessagingHandler();
	void SetupWidgets();
	void UpdateFromDruidsSageMessage(const FDruidsSageChatMessage* Message);
	void UpdateFromThinkingJson(const TArray<TSharedPtr<FJsonValue>>& ThinkingArray);
	UIDruidsSageChatItem* CreateNewChatItem(const TSharedPtr<FJsonValue>& ContentJson);
	void UpdateChatItem(UIDruidsSageChatItem* ChatItem, const TSharedPtr<FJsonValue>& ContentJson);

	UFUNCTION()
	void HandleActionRequestApplied(const FString& JsonString, UDruidsSageActionRequestChatItem* ChatItem);

	UFUNCTION()
	void HandleSuggestedPromptSelected(const FString& PromptText, UDruidsSageSuggestedPromptsChatItem* ChatItem);

	// Static helper functions
	static FString GetContentType(const TSharedPtr<FJsonValue>& ContentJson);
	static TSharedPtr<FJsonValueArray> CreateTextContentJson(const FString& Content);
	static FString CreateProcessingText();
	static bool ChatItemMatchesType(UIDruidsSageChatItem* Widget, const FString& Type);
};
