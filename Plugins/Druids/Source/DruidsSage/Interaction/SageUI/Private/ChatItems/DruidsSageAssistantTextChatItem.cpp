#include "ChatItems/DruidsSageAssistantTextChatItem.h"
#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonSerializer.h>
#include "Framework/Application/SlateApplication.h"
#include "Windows/WindowsPlatformApplicationMisc.h"

#include "DruidsSageMessagingHandler.h"
#include "MarkdownRichTextBlock.h"

UDruidsSageAssistantTextChatItem::UDruidsSageAssistantTextChatItem(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
	, ChatText(TEXT(""))
	, MessageWidget(nullptr)
{
}

void UDruidsSageAssistantTextChatItem::NativePreConstruct()
{
	Super::NativePreConstruct();
	UpdateWidgetStyling();
}

void UDruidsSageAssistantTextChatItem::NativeConstruct()
{
	Super::NativeConstruct();
	UpdateWidgetStyling();
}

void UDruidsSageAssistantTextChatItem::SynchronizeProperties()
{
	Super::SynchronizeProperties();
	UpdateWidgetStyling();
}

FName UDruidsSageAssistantTextChatItem::GetTypeName() const
{
	return GetClassName();
}

void UDruidsSageAssistantTextChatItem::FillInDruidsMessage(FDruidsSageChatMessage& Message) const
{
	Message.SetRole(EDruidsSageChatRole::Assistant);

	// Create proper JSON content array
	TArray<TSharedPtr<FJsonValue>> ContentArray;
	TSharedPtr<FJsonObject> TextContent = MakeShared<FJsonObject>();
	TextContent->SetStringField("type", "text");
	TextContent->SetStringField("text", ChatText);
	ContentArray.Add(MakeShared<FJsonValueObject>(TextContent));

	Message.SetContentArray(ContentArray);
}

EDruidsSageChatRole UDruidsSageAssistantTextChatItem::GetMessageRole() const
{
	return EDruidsSageChatRole::Assistant;
}

TWeakObjectPtr<UDruidsSageMessagingHandler> UDruidsSageAssistantTextChatItem::GetMessagingHandler() const
{
	return MessagingHandler;
}

void UDruidsSageAssistantTextChatItem::UpdateFromContentJson(const TSharedPtr<FJsonValue>& ContentJson)
{
	TSharedPtr<FJsonObject>* JsonObject = nullptr;
	if (!ContentJson.IsValid() || !ContentJson->TryGetObject(JsonObject) || !JsonObject)
	{
		return;
	}

	FString Type;
	(*JsonObject)->TryGetStringField(TEXT("type"), Type);
	if (Type == TEXT("text"))
	{
		FString Text;
		(*JsonObject)->TryGetStringField(TEXT("text"), Text);
		
		ChatText = Text;
		if (MessageWidget)
		{
			MessageWidget->SetNewText(FText::FromString(Text));
		}
	}
}

void UDruidsSageAssistantTextChatItem::InitializeAssistantTextChatItem(const FString& InChatText)
{
	ChatText = InChatText;

	UpdateWidgetStyling();
}

void UDruidsSageAssistantTextChatItem::SetScrollBoxReference(UScrollBox* InScrollBox)
{
	SetupMessagingHandler(InScrollBox);
}

void UDruidsSageAssistantTextChatItem::SetupMessagingHandler(UScrollBox* ScrollBox)
{
	MessagingHandler = NewObject<UDruidsSageMessagingHandler>();
	MessagingHandler->SetFlags(RF_Standalone);
	MessagingHandler->ScrollBoxReference = ScrollBox;

	MessagingHandler->OnMessageContentUpdated.BindLambda([this](FString Content)
	{
		if (!MessageWidget)
		{
			return;
		}

		ChatText = Content;
		MessageWidget->SetNewText(FText::FromString(Content));
	});
}

void UDruidsSageAssistantTextChatItem::UpdateWidgetStyling()
{
	if (!MessageWidget)
	{
		return;
	}

	MessageWidget->SetNewText(FText::FromString(ChatText));
}

void UDruidsSageAssistantTextChatItem::CopyMarkdownToClipboard()
{
	if (MessageWidget)
	{
		FString MarkdownText = MessageWidget->GetOriginalMarkdownText();
		if (!MarkdownText.IsEmpty())
		{
			FPlatformApplicationMisc::ClipboardCopy(*MarkdownText);
		}
	}
}

void UDruidsSageAssistantTextChatItem::CopyPlainTextToClipboard()
{
	if (MessageWidget)
	{
		FString PlainText = MessageWidget->GetPlainText();
		if (!PlainText.IsEmpty())
		{
			FPlatformApplicationMisc::ClipboardCopy(*PlainText);
		}
	}
}

FString UDruidsSageAssistantTextChatItem::GetPlainText() const
{
	if (MessageWidget)
	{
		return MessageWidget->GetPlainText();
	}
	return FString();
}
