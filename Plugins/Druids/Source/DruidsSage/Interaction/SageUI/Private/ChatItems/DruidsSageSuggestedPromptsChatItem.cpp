#include "ChatItems/DruidsSageSuggestedPromptsChatItem.h"
#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonSerializer.h>

#include "DruidsSageMessagingHandler.h"
#include "Components/TextBlock.h"
#include "Components/Button.h"
#include "Components/ScrollBox.h"
#include "ISuggestedPromptsHandler.h"
#include "DruidsSageSuggestedPrompts.h"

UDruidsSageSuggestedPromptsChatItem::UDruidsSageSuggestedPromptsChatItem(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
	, PromptButton1(nullptr)
	, PromptButton2(nullptr)
	, PromptButton1Text(nullptr)
	, PromptButton2Text(nullptr)
	, ContentJsonValue(nullptr)
{
}

void UDruidsSageSuggestedPromptsChatItem::NativePreConstruct()
{
	Super::NativePreConstruct();
	SetupWidgets();
}

void UDruidsSageSuggestedPromptsChatItem::NativeConstruct()
{
	Super::NativeConstruct();
	SetupWidgets();

	if (PromptButton1)
	{
		PromptButton1->OnClicked.AddDynamic(this, &UDruidsSageSuggestedPromptsChatItem::OnPromptButton1Clicked);
	}

	if (PromptButton2)
	{
		PromptButton2->OnClicked.AddDynamic(this, &UDruidsSageSuggestedPromptsChatItem::OnPromptButton2Clicked);
	}
}

void UDruidsSageSuggestedPromptsChatItem::SynchronizeProperties()
{
	Super::SynchronizeProperties();
	SetupWidgets();
}

FName UDruidsSageSuggestedPromptsChatItem::GetTypeName() const
{
	return GetClassName();
}

void UDruidsSageSuggestedPromptsChatItem::FillInDruidsMessage(FDruidsSageChatMessage& Message) const
{
	Message.SetRole(EDruidsSageChatRole::Assistant);

	// Include the suggested prompts content in the message
	// Note: The logic for whether to include this in chat history will be handled
	// by the parent assistant chat item based on whether it's the last one
	if (ContentJsonValue.IsValid())
	{
		TArray<TSharedPtr<FJsonValue>> ContentArray;
		ContentArray.Add(ContentJsonValue);
		Message.SetContentArray(ContentArray);
	}
	else
	{
		// Create suggested prompts content from current state if no JSON content
		TSharedPtr<FJsonObject> SuggestedPromptsContent = MakeShared<FJsonObject>();
		SuggestedPromptsContent->SetStringField("type", "suggested_prompts");

		// Create prompts array
		TArray<TSharedPtr<FJsonValue>> PromptsArray;

		if (!SummaryText1.IsEmpty() && !PromptText1.IsEmpty())
		{
			TSharedPtr<FJsonObject> Prompt1 = MakeShared<FJsonObject>();
			Prompt1->SetStringField("summary", SummaryText1);
			Prompt1->SetStringField("prompt", PromptText1);
			PromptsArray.Add(MakeShared<FJsonValueObject>(Prompt1));
		}

		if (!SummaryText2.IsEmpty() && !PromptText2.IsEmpty())
		{
			TSharedPtr<FJsonObject> Prompt2 = MakeShared<FJsonObject>();
			Prompt2->SetStringField("summary", SummaryText2);
			Prompt2->SetStringField("prompt", PromptText2);
			PromptsArray.Add(MakeShared<FJsonValueObject>(Prompt2));
		}

		if (!PromptsArray.IsEmpty())
		{
			SuggestedPromptsContent->SetArrayField("prompts", PromptsArray);

			TArray<TSharedPtr<FJsonValue>> ContentArray;
			ContentArray.Add(MakeShared<FJsonValueObject>(SuggestedPromptsContent));
			Message.SetContentArray(ContentArray);
		}
		else
		{
			// No prompts to save
			TArray<TSharedPtr<FJsonValue>> EmptyContentArray;
			Message.SetContentArray(EmptyContentArray);
		}
	}
}

EDruidsSageChatRole UDruidsSageSuggestedPromptsChatItem::GetMessageRole() const
{
	return EDruidsSageChatRole::Assistant;
}

TWeakObjectPtr<UDruidsSageMessagingHandler> UDruidsSageSuggestedPromptsChatItem::GetMessagingHandler() const
{
	return MessagingHandler;
}

void UDruidsSageSuggestedPromptsChatItem::UpdateFromContentJson(const TSharedPtr<FJsonValue>& ContentJson)
{
	ContentJsonValue = ContentJson;

	TSharedPtr<FJsonObject>* JsonObject = nullptr;
	if (!ContentJson.IsValid() || !ContentJson->TryGetObject(JsonObject) || !JsonObject)
	{
		return;
	}

	// Parse the suggested_prompts object
	const TSharedPtr<FJsonObject>* SuggestedPromptsObject = nullptr;
	if ((*JsonObject)->TryGetObjectField(TEXT("suggested_prompts"), SuggestedPromptsObject))
	{
		// Get the current_conversation array
		const TArray<TSharedPtr<FJsonValue>>* CurrentConversationArray = nullptr;
		if ((*SuggestedPromptsObject)->TryGetArrayField(TEXT("current_conversation"), CurrentConversationArray))
		{
			// Extract the first two items for the buttons
			if (CurrentConversationArray->Num() > 0)
			{
				const TSharedPtr<FJsonObject>* FirstPromptObject = nullptr;
				if ((*CurrentConversationArray)[0]->TryGetObject(FirstPromptObject))
				{
					(*FirstPromptObject)->TryGetStringField(TEXT("summary"), SummaryText1);
					(*FirstPromptObject)->TryGetStringField(TEXT("prompt"), PromptText1);

					if (PromptButton1)
					{
						PromptButton1->SetToolTipText(FText::FromString(SummaryText1));
					}

					if (PromptButton1Text)
					{
						PromptButton1Text->SetText(FText::FromString(SummaryText1));
					}
				}
			}

			if (CurrentConversationArray->Num() > 1)
			{
				const TSharedPtr<FJsonObject>* SecondPromptObject = nullptr;
				if ((*CurrentConversationArray)[1]->TryGetObject(SecondPromptObject))
				{
					(*SecondPromptObject)->TryGetStringField(TEXT("summary"), SummaryText2);
					(*SecondPromptObject)->TryGetStringField(TEXT("prompt"), PromptText2);

					if (PromptButton2)
					{
						PromptButton2->SetToolTipText(FText::FromString(SummaryText2));
					}

					if (PromptButton2Text)
					{
						PromptButton2Text->SetText(FText::FromString(SummaryText2));
					}
				}
			}

			// Process fresh_conversation prompts if we have a handler
			if (SuggestedPromptsHandler.IsValid())
			{
				const TArray<TSharedPtr<FJsonValue>>* FreshConversationArray = nullptr;
				if ((*SuggestedPromptsObject)->TryGetArrayField(TEXT("fresh_conversation"), FreshConversationArray))
				{
					SuggestedPromptsHandler->ProcessFreshConversationPrompts(*FreshConversationArray);
				}
			}
		}
	}
}

void UDruidsSageSuggestedPromptsChatItem::SetScrollBoxReference(UScrollBox* InScrollBox)
{
	MessagingHandler = NewObject<UDruidsSageMessagingHandler>();
	MessagingHandler->SetFlags(RF_Standalone);
	MessagingHandler->ScrollBoxReference = InScrollBox;
}

void UDruidsSageSuggestedPromptsChatItem::SetSuggestedPromptsHandler(TSharedPtr<ISuggestedPromptsHandler> Handler)
{
	SuggestedPromptsHandler = Handler;
}

void UDruidsSageSuggestedPromptsChatItem::PopulateFromSavedPrompts()
{
	if (!SuggestedPromptsHandler.IsValid())
	{
		return;
	}

	// Get saved prompts from the handler
	TArray<FSuggestedPromptEntry> SavedPrompts = SuggestedPromptsHandler->GetSuggestedPrompts();

	// Clear current prompts
	SummaryText1.Empty();
	PromptText1.Empty();
	SummaryText2.Empty();
	PromptText2.Empty();

	// Populate with saved prompts (up to 2)
	if (SavedPrompts.Num() > 0)
	{
		SummaryText1 = SavedPrompts[0].Summary;
		PromptText1 = SavedPrompts[0].Prompt;

		if (PromptButton1)
		{
			PromptButton1->SetToolTipText(FText::FromString(SummaryText1));
		}

		if (PromptButton1Text)
		{
			PromptButton1Text->SetText(FText::FromString(SummaryText1));
		}
	}

	if (SavedPrompts.Num() > 1)
	{
		SummaryText2 = SavedPrompts[1].Summary;
		PromptText2 = SavedPrompts[1].Prompt;

		if (PromptButton2)
		{
			PromptButton2->SetToolTipText(FText::FromString(SummaryText2));
		}

		if (PromptButton2Text)
		{
			PromptButton2Text->SetText(FText::FromString(SummaryText2));
		}
	}

	// Update visibility based on whether we have prompts
	if (SavedPrompts.Num() == 0)
	{
		SetVisibility(ESlateVisibility::Collapsed);
	}
	else
	{
		SetVisibility(ESlateVisibility::Visible);
	}
}

void UDruidsSageSuggestedPromptsChatItem::InitializeWithPrompts(const FString& Summary1, const FString& Prompt1, const FString& Summary2, const FString& Prompt2)
{
	// Set the prompt data
	SummaryText1 = Summary1;
	PromptText1 = Prompt1;
	SummaryText2 = Summary2;
	PromptText2 = Prompt2;

	// Update the UI elements for first prompt
	if (!Summary1.IsEmpty() && !Prompt1.IsEmpty())
	{
		if (PromptButton1)
		{
			PromptButton1->SetToolTipText(FText::FromString(Summary1));
			PromptButton1->SetVisibility(ESlateVisibility::Visible);
		}

		if (PromptButton1Text)
		{
			PromptButton1Text->SetText(FText::FromString(Summary1));
		}
	}
	else
	{
		if (PromptButton1)
		{
			PromptButton1->SetVisibility(ESlateVisibility::Collapsed);
		}
	}

	// Update the UI elements for second prompt
	if (!Summary2.IsEmpty() && !Prompt2.IsEmpty())
	{
		if (PromptButton2)
		{
			PromptButton2->SetToolTipText(FText::FromString(Summary2));
			PromptButton2->SetVisibility(ESlateVisibility::Visible);
		}

		if (PromptButton2Text)
		{
			PromptButton2Text->SetText(FText::FromString(Summary2));
		}
	}
	else
	{
		if (PromptButton2)
		{
			PromptButton2->SetVisibility(ESlateVisibility::Collapsed);
		}
	}

	// Show the widget if we have at least one prompt
	if ((!Summary1.IsEmpty() && !Prompt1.IsEmpty()) || (!Summary2.IsEmpty() && !Prompt2.IsEmpty()))
	{
		SetVisibility(ESlateVisibility::Visible);
	}
	else
	{
		SetVisibility(ESlateVisibility::Collapsed);
	}
}

void UDruidsSageSuggestedPromptsChatItem::OnPromptButton1Clicked()
{
	if (OnSuggestedPromptSelected.IsBound() && !PromptText1.IsEmpty())
	{
		// Mark the prompt as used if we have a handler
		if (SuggestedPromptsHandler.IsValid())
		{
			SuggestedPromptsHandler->MarkPromptAsUsed(SummaryText1, PromptText1);
		}

		OnSuggestedPromptSelected.Broadcast(PromptText1, this);
	}
}

void UDruidsSageSuggestedPromptsChatItem::OnPromptButton2Clicked()
{
	if (OnSuggestedPromptSelected.IsBound() && !PromptText2.IsEmpty())
	{
		// Mark the prompt as used if we have a handler
		if (SuggestedPromptsHandler.IsValid())
		{
			SuggestedPromptsHandler->MarkPromptAsUsed(SummaryText2, PromptText2);
		}

		OnSuggestedPromptSelected.Broadcast(PromptText2, this);
	}
}

void UDruidsSageSuggestedPromptsChatItem::SetupWidgets()
{
	// This method is called to ensure widget references are properly set up
	// The actual widget binding happens through the BindWidget meta tag
}

FString UDruidsSageSuggestedPromptsChatItem::GetPlainText() const
{
	// Return a plain text representation of the suggested prompts
	FString PlainText = TEXT("Suggested Prompts:");

	if (!SummaryText1.IsEmpty())
	{
		PlainText += TEXT("\n1. ") + SummaryText1;
	}

	if (!SummaryText2.IsEmpty())
	{
		PlainText += TEXT("\n2. ") + SummaryText2;
	}

	return PlainText;
}

void UDruidsSageSuggestedPromptsChatItem::OnPromptMessageSent()
{
	// Hide the suggested prompts when any prompt message is sent
	SetVisibility(ESlateVisibility::Collapsed);
}
