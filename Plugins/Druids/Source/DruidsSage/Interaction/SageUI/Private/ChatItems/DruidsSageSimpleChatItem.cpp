#include "ChatItems/DruidsSageSimpleChatItem.h"
#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonSerializer.h>

#include "Components/TextBlock.h"
#include "Components/ScrollBox.h"

#include "Framework/Application/SlateApplication.h"
#include "Windows/WindowsPlatformApplicationMisc.h"

#include "MarkdownRichTextBlock.h"

#include "DruidsSageMessagingHandler.h"
#include "SageExtensionTypes.h"

UDruidsSageSimpleChatItem::UDruidsSageSimpleChatItem(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
	, MessageRole(EDruidsSageChatRole::User)
	, ChatText(TEXT(""))
	, RoleWidget(nullptr)
	, MessageWidget(nullptr)
{
}

void UDruidsSageSimpleChatItem::NativePreConstruct()
{
	Super::NativePreConstruct();
	UpdateWidgetStyling();
}

void UDruidsSageSimpleChatItem::NativeConstruct()
{
	Super::NativeConstruct();
	UpdateWidgetStyling();
}

void UDruidsSageSimpleChatItem::SynchronizeProperties()
{
	Super::SynchronizeProperties();
	UpdateWidgetStyling();
}

FName UDruidsSageSimpleChatItem::GetTypeName() const
{
	return GetClassName();
}

void UDruidsSageSimpleChatItem::FillInDruidsMessage(FDruidsSageChatMessage& Message) const
{
	Message.SetRole(MessageRole);

	// Create proper JSON content array instead of using legacy ChatContent
	TArray<TSharedPtr<FJsonValue>> ContentArray;

	// Add text content
	TSharedPtr<FJsonObject> TextContent = MakeShared<FJsonObject>();
	TextContent->SetStringField("type", "text");
	TextContent->SetStringField("text", ChatText);
	ContentArray.Add(MakeShared<FJsonValueObject>(TextContent));

	// For user messages, we only want the text content - no context data
	// For assistant/system messages, we can include extensions if needed
	if (MessageRole != EDruidsSageChatRole::User)
	{
		// Add extension content for non-user messages if extensions are active
		for (const TSharedPtr<FDruidsSageExtensionDefinition>& Extension : ActiveExtensionDefinitions)
		{
			if (Extension.IsValid())
			{
				TSharedPtr<FJsonObject> ExtensionContent = MakeShared<FJsonObject>();
				ExtensionContent->SetStringField("type", "extension");
				ExtensionContent->SetObjectField("extension", Extension->GetExtensionDefinitionJson());
				ContentArray.Add(MakeShared<FJsonValueObject>(ExtensionContent));
			}
		}
	}

	Message.SetContentArray(ContentArray);
}

EDruidsSageChatRole UDruidsSageSimpleChatItem::GetMessageRole() const
{
	return MessageRole;
}

TWeakObjectPtr<UDruidsSageMessagingHandler> UDruidsSageSimpleChatItem::GetMessagingHandler() const
{
	return MessagingHandler;
}

void UDruidsSageSimpleChatItem::UpdateFromContentJson(const TSharedPtr<FJsonValue>& ContentJson)
{
	TSharedPtr<FJsonObject>* JsonObject = nullptr;
	if (!ContentJson.IsValid() || !ContentJson->TryGetObject(JsonObject) || !JsonObject)
	{
		return;
	}

	FString Type;
	(*JsonObject)->TryGetStringField(TEXT("type"), Type);
	if (Type == TEXT("text"))
	{
		FString Text;
		(*JsonObject)->TryGetStringField(TEXT("text"), Text);
		
		ChatText = Text;
		if (MessageWidget)
		{
			MessageWidget->SetNewText(FText::FromString(Text));
		}
	}
}

void UDruidsSageSimpleChatItem::InitializeSimpleChatItem(EDruidsSageChatRole InMessageRole, const FString& InChatText, 
	const TArray<TSharedPtr<FDruidsSageExtensionDefinition>>& InActiveExtensionDefinitions)
{
	MessageRole = InMessageRole;
	ChatText = InChatText;
	ActiveExtensionDefinitions = InActiveExtensionDefinitions;

	UpdateWidgetStyling();
}

void UDruidsSageSimpleChatItem::SetScrollBoxReference(UScrollBox* InScrollBox)
{
	SetupMessagingHandler(InScrollBox);
}

void UDruidsSageSimpleChatItem::SetupMessagingHandler(UScrollBox* ScrollBox)
{
	if (MessageRole == EDruidsSageChatRole::Assistant)
	{
		MessagingHandler = NewObject<UDruidsSageMessagingHandler>();
		MessagingHandler->SetFlags(RF_Standalone);
		MessagingHandler->ScrollBoxReference = ScrollBox;

		MessagingHandler->OnMessageContentUpdated.BindLambda([this](FString Content)
		{
			if (!MessageWidget)
			{
				return;
			}

			ChatText = Content;
			MessageWidget->SetNewText(FText::FromString(Content));
		});
	}
}

void UDruidsSageSimpleChatItem::UpdateWidgetStyling()
{
	if (!RoleWidget || !MessageWidget)
	{
		return;
	}

	FText RoleText = FText::FromString(TEXT("User:"));
	
	if (MessageRole == EDruidsSageChatRole::Assistant)
	{
		RoleText = FText::FromString(TEXT("Response:"));
	}
	else if (MessageRole == EDruidsSageChatRole::System)
	{
		RoleText = FText::FromString(TEXT("System:"));
	}

	RoleWidget->SetText(RoleText);
	MessageWidget->SetNewText(FText::FromString(ChatText));
}

void UDruidsSageSimpleChatItem::CopyMarkdownToClipboard()
{
	if (MessageWidget)
	{
		FString MarkdownText = MessageWidget->GetOriginalMarkdownText();
		if (!MarkdownText.IsEmpty())
		{
			FPlatformApplicationMisc::ClipboardCopy(*MarkdownText);
		}
	}
}

void UDruidsSageSimpleChatItem::CopyPlainTextToClipboard()
{
	if (MessageWidget)
	{
		FString PlainText = MessageWidget->GetPlainText();
		if (!PlainText.IsEmpty())
		{
			FPlatformApplicationMisc::ClipboardCopy(*PlainText);
		}
	}
}

FString UDruidsSageSimpleChatItem::GetPlainText() const
{
	if (MessageWidget)
	{
		return MessageWidget->GetPlainText();
	}
	return FString();
}
