#include "ChatItems/DruidsSageAssistantChatItem.h"
#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonSerializer.h>

#include "LogDruids.h"

#include "Components/TextBlock.h"
#include "Components/VerticalBox.h"
#include "Components/VerticalBoxSlot.h"

#include "SageUIModule.h"

#include "ChatWidgetOverrides.h"

#include "DruidsSageMessagingHandler.h"

#include "ChatItems/DruidsSageSimpleChatItem.h"
#include "ChatItems/DruidsSageActionRequestChatItem.h"
#include "ChatItems/DruidsSageSuggestedPromptsChatItem.h"
#include "ChatItems/DruidsSageAssistantTextChatItem.h"
#include "ChatItems/DruidsSageThinkingContainerChatItem.h"
#include "ISuggestedPromptsHandler.h"

UDruidsSageAssistantChatItem::UDruidsSageAssistantChatItem(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
	, RoleWidget(nullptr)
	, MessageContainer(nullptr)
	, PreviousContentJson(nullptr)
{
}

void UDruidsSageAssistantChatItem::NativePreConstruct()
{
	Super::NativePreConstruct();
	SetupWidgets();
}

void UDruidsSageAssistantChatItem::NativeConstruct()
{
	Super::NativeConstruct();
	SetupWidgets();
	SetupMessagingHandler();
}

void UDruidsSageAssistantChatItem::SynchronizeProperties()
{
	Super::SynchronizeProperties();
	SetupWidgets();
}

FName UDruidsSageAssistantChatItem::GetTypeName() const
{
	return GetClassName();
}

void UDruidsSageAssistantChatItem::FillInDruidsMessage(FDruidsSageChatMessage& Message) const
{
	Message.SetRole(EDruidsSageChatRole::Assistant);

	TArray<TSharedPtr<FJsonValue>> FinalContentArray;

	// Get content from PreviousContentJson if available
	if (PreviousContentJson.IsValid())
	{
		TArray<TSharedPtr<FJsonValue>>* ContentArray;
		if (PreviousContentJson->TryGetArray(ContentArray))
		{
			// Filter out suggested prompts if this is not the last assistant chat item
			for (const TSharedPtr<FJsonValue>& ContentItem : *ContentArray)
			{
				bool bShouldInclude = true;

				// Check if this is a suggested prompts item
				TSharedPtr<FJsonObject>* JsonObject = nullptr;
				if (ContentItem.IsValid() && ContentItem->TryGetObject(JsonObject) && JsonObject)
				{
					FString ContentType;
					if ((*JsonObject)->TryGetStringField(TEXT("type"), ContentType) && ContentType == TEXT("suggested_prompts"))
					{
						// Only include suggested prompts if this is the last assistant chat item
						bShouldInclude = bIsLastAssistantChatItem;
					}
				}

				if (bShouldInclude)
				{
					FinalContentArray.Add(ContentItem);
				}
			}
		}
		else
		{
			// If ContentJson isn't already an array, wrap it in one
			FinalContentArray.Add(PreviousContentJson);
		}
	}

	Message.SetContentArray(FinalContentArray);
}

EDruidsSageChatRole UDruidsSageAssistantChatItem::GetMessageRole() const
{
	return EDruidsSageChatRole::Assistant;
}

TWeakObjectPtr<UDruidsSageMessagingHandler> UDruidsSageAssistantChatItem::GetMessagingHandler() const
{
	return MessagingHandler;
}

void UDruidsSageAssistantChatItem::UpdateFromContentJson(const TSharedPtr<FJsonValue>& ContentJson)
{
	PreviousContentJson = ContentJson;
	
	FString OutputString;
	const TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
	FJsonSerializer::Serialize(ContentJson, TEXT(""), Writer);
    
	UE_LOG(LogDruidsSage_Internal, Display, TEXT("UDruidsSageAssistantChatItem::UpdateFromContentJsonCpp: %s"), *OutputString);
	
	//Update from a content array
	const TArray<TSharedPtr<FJsonValue>>* ContentArray = nullptr;
	if (!ContentJson->TryGetArray(ContentArray))
	{
		return;
	}

	if (!MessageContainer)
	{
		return;
	}

	int32 NumExistingWidgets = ChildChatItems.Num();
	
	for (int32 MessageIndex = 0; MessageIndex < ContentArray->Num(); MessageIndex++)
	{
		const TSharedPtr<FJsonValue>& JsonValue = (*ContentArray)[MessageIndex];
        
		if (MessageIndex < NumExistingWidgets)
		{
			// Update existing widget
			UIDruidsSageChatItem* DruidsSageChatItem = ChildChatItems[MessageIndex];
			if (!DruidsSageChatItem)
			{
				continue;
			}

			if (FString ContentType = GetContentType(JsonValue); ChatItemMatchesType(DruidsSageChatItem, ContentType))
			{
				UpdateChatItem(DruidsSageChatItem, JsonValue);
			}
			else
			{
				// Replace widget with new type
				MessageContainer->RemoveChild(DruidsSageChatItem);
				UIDruidsSageChatItem* NewChatItem = CreateNewChatItem(JsonValue);
				if (NewChatItem)
				{
					ChildChatItems[MessageIndex] = NewChatItem;
					MessageContainer->AddChildToVerticalBox(NewChatItem);
				}
			}
		}
		else
		{
			// Add new widget
			UIDruidsSageChatItem* NewChatItem = CreateNewChatItem(JsonValue);
			if (NewChatItem)
			{
				ChildChatItems.Add(NewChatItem);
				MessageContainer->AddChildToVerticalBox(NewChatItem);
			}
		}
	}
}

void UDruidsSageAssistantChatItem::InitializeAssistantChatItem()
{
	SetupWidgets();
	SetupMessagingHandler();
}

void UDruidsSageAssistantChatItem::SetScrollBoxReference(UScrollBox* InScrollBox)
{
	if (MessagingHandler.IsValid())
	{
		MessagingHandler->ScrollBoxReference = InScrollBox;
	}
}

void UDruidsSageAssistantChatItem::SetRawText(const FString& ContentText)
{
	const TSharedPtr<FJsonValueArray> JsonValueArray = CreateTextContentJson(ContentText);
	UpdateFromContentJson(JsonValueArray);
}

void UDruidsSageAssistantChatItem::SetIsLastAssistantChatItem(bool bIsLast)
{
	bIsLastAssistantChatItem = bIsLast;
}

void UDruidsSageAssistantChatItem::SetSuggestedPromptsHandler(TSharedPtr<ISuggestedPromptsHandler> Handler)
{
	SuggestedPromptsHandler = Handler;
}

void UDruidsSageAssistantChatItem::SetupMessagingHandler()
{
	MessagingHandler = NewObject<UDruidsSageMessagingHandler>();
	MessagingHandler->SetFlags(RF_Standalone);

	MessagingHandler->OnMessageRequestSent.BindLambda([this](FString Content)
	{
		SetRawText(Content);
	});

	MessagingHandler->OnMessageRequestFailed.BindLambda([this](FString Content)
	{
		SetRawText(Content);
	});

	MessagingHandler->OnMessageResponseUpdated.BindLambda([this](const FDruidsSageChatResponse& Response)
	{
		if (Response.bSuccess)
		{
			if (!Response.Choices.IsEmpty())
			{
				UpdateFromDruidsSageMessage(&Response.Choices.Top().Message);
			}
			else
			{
//				FString Content = CreateProcessingText();
				FString Content("Request sent...");
				SetRawText(Content);
			}
		}
		else
		{
			UE_LOG(LogDruidsSage, Error, TEXT("UDruidsSageAssistantChatItem::OnMessageResponseUpdated: Response is not valid."));
		}
	});

	MessagingHandler->OnMessageResponseCompleted.BindLambda([this]()
	{
		OnChatResponseCompleted.Broadcast();
	});
}

void UDruidsSageAssistantChatItem::SetupWidgets()
{
	if (RoleWidget)
	{
		RoleWidget->SetText(FText::FromString(TEXT("Sage:")));
	}
}

void UDruidsSageAssistantChatItem::UpdateFromDruidsSageMessage(const FDruidsSageChatMessage* Message)
{
	const TArray<TSharedPtr<FJsonValue>>& ContentArray = Message->GetContentArray();
	const TSharedPtr<FJsonValue> JsonValue = MakeShared<FJsonValueArray>(ContentArray);
	UpdateFromContentJson(JsonValue);

	TArray<TSharedPtr<FJsonValue>> JsonValues = Message->GetThinkingArray();
	UpdateFromThinkingJson(JsonValues);
}

void UDruidsSageAssistantChatItem::UpdateFromThinkingJson(const TArray<TSharedPtr<FJsonValue>>& ThinkingArray)
{
	// Only create thinking container if we have thinking data
	if (ThinkingArray.IsEmpty())
	{
		return;
	}

	if (!MessageContainer)
	{
		return;
	}

	// Look for existing thinking container in our child items
	UDruidsSageThinkingContainerChatItem* ExistingThinkingContainer = nullptr;
	for (UIDruidsSageChatItem* ChildItem : ChildChatItems)
	{
		if (UDruidsSageThinkingContainerChatItem* ThinkingContainer = Cast<UDruidsSageThinkingContainerChatItem>(ChildItem))
		{
			ExistingThinkingContainer = ThinkingContainer;
			break;
		}
	}

	UDruidsSageThinkingContainerChatItem* ThinkingContainer = ExistingThinkingContainer;

	// Create new thinking container if we don't have one
	if (!ThinkingContainer)
	{
		// Get the widget class from overrides
		TSubclassOf<UDruidsSageThinkingContainerChatItem> WidgetClass = UDruidsSageThinkingContainerChatItem::StaticClass();
		if (FSageUIModule* SageUIModule = FModuleManager::GetModulePtr<FSageUIModule>("SageUI"))
		{
			if (UChatWidgetOverrides* Overrides = SageUIModule->GetChatWidgetOverrides())
			{
				if (TSubclassOf<UDruidsSageThinkingContainerChatItem> OverrideClass = Overrides->GetThinkingContainerChatItemWidgetClass())
				{
					WidgetClass = OverrideClass;
				}
			}
		}

		ThinkingContainer = CreateWidget<UDruidsSageThinkingContainerChatItem>(this, WidgetClass);
		if (ThinkingContainer && MessagingHandler.IsValid())
		{
			ThinkingContainer->InitializeThinkingContainerChatItem(TEXT("Thinking"));
			ThinkingContainer->SetScrollBoxReference(MessagingHandler->ScrollBoxReference);

			// Add to our child items and container
			ChildChatItems.Add(ThinkingContainer);
			MessageContainer->AddChildToVerticalBox(ThinkingContainer);
		}
	}

	// Update the thinking container with new data
	if (ThinkingContainer)
	{
		// Create a JSON object that matches the expected format for thinking container
		TSharedPtr<FJsonObject> ThinkingContainerJson = MakeShared<FJsonObject>();
		ThinkingContainerJson->SetStringField(TEXT("type"), TEXT("thinking_container"));
		ThinkingContainerJson->SetStringField(TEXT("title"), TEXT("Thinking"));
		ThinkingContainerJson->SetArrayField(TEXT("thinking_items"), ThinkingArray);

		// Update the container with the thinking data
		TSharedPtr<FJsonValue> ThinkingContainerValue = MakeShared<FJsonValueObject>(ThinkingContainerJson);
		ThinkingContainer->UpdateFromContentJson(ThinkingContainerValue);
	}
}

UIDruidsSageChatItem* UDruidsSageAssistantChatItem::CreateNewChatItem(const TSharedPtr<FJsonValue>& ContentJson)
{
	FString ContentType = GetContentType(ContentJson);

	TSharedPtr<FJsonObject>* JsonObject = nullptr;
	if (ContentJson.Get()->TryGetObject(JsonObject))
	{
		if (ContentType == TEXT("text"))
		{
			FString Text;
			JsonObject->Get()->TryGetStringField(TEXT("text"), Text);

			// Get the widget class from overrides
			TSubclassOf<UDruidsSageAssistantTextChatItem> WidgetClass = UDruidsSageAssistantTextChatItem::StaticClass();
			if (FSageUIModule* SageUIModule = FModuleManager::GetModulePtr<FSageUIModule>("SageUI"))
			{
				if (UChatWidgetOverrides* Overrides = SageUIModule->GetChatWidgetOverrides())
				{
					if (TSubclassOf<UDruidsSageAssistantTextChatItem> OverrideClass = Overrides->GetAssistantTextChatItemWidgetClass())
					{
						WidgetClass = OverrideClass;
					}
				}
			}

			UDruidsSageAssistantTextChatItem* AssistantTextChatItem = CreateWidget<UDruidsSageAssistantTextChatItem>(this, WidgetClass);
			if (AssistantTextChatItem && MessagingHandler.IsValid())
			{
				AssistantTextChatItem->InitializeAssistantTextChatItem(Text);
				AssistantTextChatItem->SetScrollBoxReference(MessagingHandler->ScrollBoxReference);
			}
			return AssistantTextChatItem;
		}
		if (ContentType == TEXT("action_request"))
		{
			if (const TSharedPtr<FJsonObject>* ActionRequest;
				JsonObject->Get()->TryGetObjectField(TEXT("action_request"), ActionRequest))
			{
				// Get the widget class from overrides
				TSubclassOf<UDruidsSageActionRequestChatItem> WidgetClass = UDruidsSageActionRequestChatItem::StaticClass();
				if (FSageUIModule* SageUIModule = FModuleManager::GetModulePtr<FSageUIModule>("SageUI"))
				{
					if (UChatWidgetOverrides* Overrides = SageUIModule->GetChatWidgetOverrides())
					{
						if (TSubclassOf<UDruidsSageActionRequestChatItem> OverrideClass = Overrides->GetActionRequestChatItemWidgetClass())
						{
							WidgetClass = OverrideClass;
						}
					}
				}

				UDruidsSageActionRequestChatItem* ActionRequestChatItem = CreateWidget<UDruidsSageActionRequestChatItem>(this, WidgetClass);
				if (ActionRequestChatItem && MessagingHandler.IsValid())
				{
					ActionRequestChatItem->SetScrollBoxReference(MessagingHandler->ScrollBoxReference);
					ActionRequestChatItem->OnActionApplied.AddDynamic(this, &UDruidsSageAssistantChatItem::HandleActionRequestApplied);

					// Track this action request chat item so we can pass results to it
					CurrentActionRequestChatItem = ActionRequestChatItem;

					TSharedPtr<FJsonValue> ActionRequestValue = MakeShared<FJsonValueObject>(*ActionRequest);
					ActionRequestChatItem->UpdateFromContentJson(ActionRequestValue);
				}
				return ActionRequestChatItem;
			}
		}
		if (ContentType == TEXT("suggested_prompts"))
		{
			// Get the widget class from overrides
			TSubclassOf<UDruidsSageSuggestedPromptsChatItem> WidgetClass = UDruidsSageSuggestedPromptsChatItem::StaticClass();
			if (FSageUIModule* SageUIModule = FModuleManager::GetModulePtr<FSageUIModule>("SageUI"))
			{
				if (UChatWidgetOverrides* Overrides = SageUIModule->GetChatWidgetOverrides())
				{
					if (TSubclassOf<UDruidsSageSuggestedPromptsChatItem> OverrideClass = Overrides->GetSuggestedPromptsChatItemWidgetClass())
					{
						WidgetClass = OverrideClass;
					}
				}
			}

			UDruidsSageSuggestedPromptsChatItem* SuggestedPromptsChatItem = CreateWidget<UDruidsSageSuggestedPromptsChatItem>(this, WidgetClass);
			if (SuggestedPromptsChatItem && MessagingHandler.IsValid())
			{
				SuggestedPromptsChatItem->SetScrollBoxReference(MessagingHandler->ScrollBoxReference);
				SuggestedPromptsChatItem->OnSuggestedPromptSelected.AddDynamic(this, &UDruidsSageAssistantChatItem::HandleSuggestedPromptSelected);

				// Inject the suggested prompts handler if available
				if (SuggestedPromptsHandler.IsValid())
				{
					SuggestedPromptsChatItem->SetSuggestedPromptsHandler(SuggestedPromptsHandler);
				}

				SuggestedPromptsChatItem->UpdateFromContentJson(ContentJson);
			}
			return SuggestedPromptsChatItem;
		}
	}

	return nullptr;
}

void UDruidsSageAssistantChatItem::UpdateChatItem(UIDruidsSageChatItem* ChatItem, const TSharedPtr<FJsonValue>& ContentJson)
{
	if (ChatItem)
	{
		ChatItem->UpdateFromContentJson(ContentJson);
	}
}

void UDruidsSageAssistantChatItem::HandleActionRequestApplied(const FString& JsonString, UDruidsSageActionRequestChatItem* ChatItem)
{
	if (OnActionApplied.IsBound())
	{
		OnActionApplied.Broadcast(JsonString, ChatItem);
	}
}

void UDruidsSageAssistantChatItem::HandleSuggestedPromptSelected(const FString& PromptText, UDruidsSageSuggestedPromptsChatItem* ChatItem)
{
	if (OnSuggestedPromptSelected.IsBound())
	{
		OnSuggestedPromptSelected.Broadcast(PromptText, ChatItem);
	}
}

void UDruidsSageAssistantChatItem::NotifyChildrenPromptMessageSent()
{
	// Notify all child chat items that a prompt message was sent
	for (UIDruidsSageChatItem* ChildItem : ChildChatItems)
	{
		if (ChildItem)
		{
			ChildItem->OnPromptMessageSent();
		}
	}
}

void UDruidsSageAssistantChatItem::HandleActionResult(bool bActionCalled, bool bSuccess, const FString& Result, const FString& ErrorMessage)
{
	// Pass the result to the current action request chat item
	if (CurrentActionRequestChatItem.IsValid())
	{
		CurrentActionRequestChatItem->HandleActionResult(bActionCalled, bSuccess, Result, ErrorMessage);
	}
}

FString UDruidsSageAssistantChatItem::GetPlainText() const
{
	FString CombinedText;

	// Concatenate plain text from all child chat items
	for (UIDruidsSageChatItem* ChildItem : ChildChatItems)
	{
		if (ChildItem)
		{
			FString ChildPlainText = ChildItem->GetPlainText();
			if (!ChildPlainText.IsEmpty())
			{
				if (!CombinedText.IsEmpty())
				{
					CombinedText += TEXT("\n");
				}
				CombinedText += ChildPlainText;
			}
		}
	}

	return CombinedText;
}

// Static helper methods
FString UDruidsSageAssistantChatItem::GetContentType(const TSharedPtr<FJsonValue>& ContentJson)
{
	TSharedPtr<FJsonObject>* JsonObject = nullptr;
	if (ContentJson.Get()->TryGetObject(JsonObject))
	{
		FString Type;
		JsonObject->Get()->TryGetStringField(TEXT("type"), Type);
		return Type;
	}
	return TEXT("Unknown");
}

TSharedPtr<FJsonValueArray> UDruidsSageAssistantChatItem::CreateTextContentJson(const FString& Content)
{
	TSharedPtr<FJsonObject> JsonObject = MakeShared<FJsonObject>();
	JsonObject->SetStringField(TEXT("type"), TEXT("text"));
	JsonObject->SetStringField(TEXT("text"), Content);

	const TArray<TSharedPtr<FJsonValue>> ContentArray {
		MakeShared<FJsonValueObject>(JsonObject)
	};
	return MakeShared<FJsonValueArray>(ContentArray);
}

FString UDruidsSageAssistantChatItem::CreateProcessingText()
{
	FString Content;

	switch (constexpr int SelectedAnimation = 1)
	{
	case 1:
		{
			static int MessageIndex = 0;
			static const TArray<FString> Messages = {
				TEXT("Processing your request"),
				TEXT("Checking your context"),
				TEXT("Understanding"),
				TEXT("Thinking a bit"),
				TEXT("Looking at what I can do"),
				TEXT("Generating a response"),
				TEXT("Figuring out what you want"),
				TEXT("Thinking some more"),
				TEXT("Considering"),
				TEXT("Consulting some references"),
				TEXT("Analyzing"),
			};

			constexpr int RepeatMessageTimes = 2;

			Content = Messages[MessageIndex / RepeatMessageTimes];

			// Rotate to next message
			MessageIndex = (MessageIndex + 1) % (Messages.Num() * RepeatMessageTimes);
		}
		break;
	default:
		Content = TEXT("Processing...");
		break;
	}

	return Content;
}

bool UDruidsSageAssistantChatItem::ChatItemMatchesType(UIDruidsSageChatItem* Widget, const FString& Type)
{
	if (!Widget)
	{
		return false;
	}

	static const TMap<FString, FName> TypeToClass = {
		{ TEXT("text"), UDruidsSageSimpleChatItem::StaticClass()->GetFName() },
		{ TEXT("action_request"), UDruidsSageActionRequestChatItem::StaticClass()->GetFName() },
		{ TEXT("suggested_prompts"), UDruidsSageSuggestedPromptsChatItem::StaticClass()->GetFName() }
	};

	if (const FName* ClassName = TypeToClass.Find(Type))
	{
		return Widget->GetTypeName() == *ClassName;
	}
	return false;
}
