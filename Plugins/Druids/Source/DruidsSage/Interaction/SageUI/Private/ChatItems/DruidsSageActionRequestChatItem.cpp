#include "ChatItems/DruidsSageActionRequestChatItem.h"
#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonSerializer.h>

#include "DruidsSageMessagingHandler.h"
#include "Components/TextBlock.h"
#include "Components/Button.h"
#include "Components/ScrollBox.h"

UDruidsSageActionRequestChatItem::UDruidsSageActionRequestChatItem(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
	, RoleWidget(nullptr)
	, MessageWidget(nullptr)
	, ApplyButton(nullptr)
	, ActionErrorMessage(nullptr)
	, FixErrorButton(nullptr)
	, ContentJsonValue(nullptr)
{
}

void UDruidsSageActionRequestChatItem::NativePreConstruct()
{
	Super::NativePreConstruct();
	SetupWidgets();
}

void UDruidsSageActionRequestChatItem::NativeConstruct()
{
	Super::NativeConstruct();
	SetupWidgets();

	if (ApplyButton)
	{
		ApplyButton->OnClicked.AddDynamic(this, &UDruidsSageActionRequestChatItem::OnApplyButtonClicked);
	}

	if (FixErrorButton)
	{
		FixErrorButton->OnClicked.AddDynamic(this, &UDruidsSageActionRequestChatItem::OnFixErrorButtonClicked);
	}
}

void UDruidsSageActionRequestChatItem::SynchronizeProperties()
{
	Super::SynchronizeProperties();
	SetupWidgets();
}

FName UDruidsSageActionRequestChatItem::GetTypeName() const
{
	return GetClassName();
}

void UDruidsSageActionRequestChatItem::FillInDruidsMessage(FDruidsSageChatMessage& Message) const
{
	Message.SetRole(EDruidsSageChatRole::Assistant);

	// Include the action request content in the message
	if (ContentJsonValue.IsValid())
	{
		TArray<TSharedPtr<FJsonValue>> ContentArray;
		ContentArray.Add(ContentJsonValue);
		Message.SetContentArray(ContentArray);
	}
	else
	{
		// Fallback to empty content array if no JSON content is available
		TArray<TSharedPtr<FJsonValue>> EmptyContentArray;
		Message.SetContentArray(EmptyContentArray);
	}
}

EDruidsSageChatRole UDruidsSageActionRequestChatItem::GetMessageRole() const
{
	return EDruidsSageChatRole::Assistant;
}

TWeakObjectPtr<UDruidsSageMessagingHandler> UDruidsSageActionRequestChatItem::GetMessagingHandler() const
{
	return MessagingHandler;
}

void UDruidsSageActionRequestChatItem::UpdateFromContentJson(const TSharedPtr<FJsonValue>& ContentJson)
{
	ContentJsonValue = ContentJson;

	TSharedPtr<FJsonObject>* JsonObject = nullptr;
	if (!ContentJson.IsValid() || !ContentJson->TryGetObject(JsonObject) || !JsonObject)
	{
		return;
	}

	FString Summary;
	if ((*JsonObject)->TryGetStringField(TEXT("summary"), Summary))
	{
		if (MessageWidget)
		{
			MessageWidget->SetText(FText::FromString(Summary));
		}
	}
}

void UDruidsSageActionRequestChatItem::InitializeActionRequestChatItem(const TScriptInterface<ISageExtensionDelegator>& InExtensionDelegator)
{
	ExtensionDelegator = InExtensionDelegator;
}

void UDruidsSageActionRequestChatItem::SetScrollBoxReference(UScrollBox* InScrollBox)
{
	MessagingHandler = NewObject<UDruidsSageMessagingHandler>();
	MessagingHandler->SetFlags(RF_Standalone);
	MessagingHandler->ScrollBoxReference = InScrollBox;
}

void UDruidsSageActionRequestChatItem::OnApplyButtonClicked()
{
	if (OnActionApplied.IsBound() && ContentJsonValue.IsValid())
	{
		// Convert TSharedPtr<FJsonValue> to FString for Blueprint compatibility
		FString OutputString;
		TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
		FJsonSerializer::Serialize(ContentJsonValue, TEXT(""), Writer);
		OnActionApplied.Broadcast(OutputString, this);
	}
}

void UDruidsSageActionRequestChatItem::OnFixErrorButtonClicked()
{
}

void UDruidsSageActionRequestChatItem::HandleActionResult(bool bActionCalled, bool bSuccess, const FString& Result, const FString& ErrorMessage)
{
	// Update the UI to show the result

	// Handle error case - show error widgets and hide apply button
	if (bActionCalled && !bSuccess)
	{
		// Show error message
		if (ActionErrorMessage)
		{
			ActionErrorMessage->SetVisibility(ESlateVisibility::Visible);
		}

		/*
		// Show fix error button
		if (FixErrorButton)
		{
			FixErrorButton->SetVisibility(ESlateVisibility::Visible);
		}

		// Hide apply button
		if (ApplyButton)
		{
			ApplyButton->SetVisibility(ESlateVisibility::Collapsed);
		}
		*/
	}
	
	// disable the apply button after action is taken
	if (ApplyButton && bActionCalled)
	{
		ApplyButton->SetIsEnabled(false);
	}

	// Broadcast the result for any listeners
	if (OnActionResult.IsBound())
	{
		OnActionResult.Broadcast(bActionCalled, bSuccess, Result, ErrorMessage);
	}
}

void UDruidsSageActionRequestChatItem::SetupWidgets()
{
	if (RoleWidget)
	{
		RoleWidget->SetText(FText::FromString(TEXT("Action Request:")));
	}

	// Hide error widgets by default
	if (ActionErrorMessage)
	{
		ActionErrorMessage->SetVisibility(ESlateVisibility::Collapsed);
	}

	if (FixErrorButton)
	{
		FixErrorButton->SetVisibility(ESlateVisibility::Collapsed);
	}
}

FString UDruidsSageActionRequestChatItem::GetPlainText() const
{
	FString Summary;

	// Extract summary from ContentJsonValue if available
	if (ContentJsonValue.IsValid())
	{
		TSharedPtr<FJsonObject>* JsonObject = nullptr;
		if (ContentJsonValue->TryGetObject(JsonObject) && JsonObject)
		{
			(*JsonObject)->TryGetStringField(TEXT("summary"), Summary);
		}
	}

	// If no summary from JSON, try to get it from the MessageWidget
	if (Summary.IsEmpty() && MessageWidget)
	{
		Summary = MessageWidget->GetText().ToString();
	}

	// Return summary with action request indicator
	if (!Summary.IsEmpty())
	{
		return Summary + TEXT(" [Action Request Available]");
	}

	return TEXT("[Action Request Available]");
}
