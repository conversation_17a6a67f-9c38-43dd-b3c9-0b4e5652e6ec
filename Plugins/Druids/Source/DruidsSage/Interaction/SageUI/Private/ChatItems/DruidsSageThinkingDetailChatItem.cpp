#include "ChatItems/DruidsSageThinkingDetailChatItem.h"
#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonSerializer.h>

#include "Components/ScrollBox.h"
#include "MarkdownRichTextBlock.h"

#include "DruidsSageMessagingHandler.h"
#include "SimpleJSON.h"

UDruidsSageThinkingDetailChatItem::UDruidsSageThinkingDetailChatItem(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
	, DetailWidget(nullptr)
{
}

void UDruidsSageThinkingDetailChatItem::NativePreConstruct()
{
	Super::NativePreConstruct();
	UpdateWidgetStyling();
}

void UDruidsSageThinkingDetailChatItem::NativeConstruct()
{
	Super::NativeConstruct();
	UpdateWidgetStyling();
}

void UDruidsSageThinkingDetailChatItem::SynchronizeProperties()
{
	Super::SynchronizeProperties();
	UpdateWidgetStyling();
}

FName UDruidsSageThinkingDetailChatItem::GetTypeName() const
{
	return GetClassName();
}

void UDruidsSageThinkingDetailChatItem::FillInDruidsMessage(FDruidsSageChatMessage& Message) const
{
	Message.SetRole(EDruidsSageChatRole::Assistant);

	// Create proper JSON content array for thinking detail item
	TArray<TSharedPtr<FJsonValue>> ContentArray;
	TSharedPtr<FJsonObject> ThinkingDetailContent = MakeShared<FJsonObject>();
	ThinkingDetailContent->SetStringField("type", "thinking_detail");
	ThinkingDetailContent->SetStringField("detail", DetailText);
	ContentArray.Add(MakeShared<FJsonValueObject>(ThinkingDetailContent));

	Message.SetContentArray(ContentArray);
}

EDruidsSageChatRole UDruidsSageThinkingDetailChatItem::GetMessageRole() const
{
	return EDruidsSageChatRole::Assistant;
}

TWeakObjectPtr<UDruidsSageMessagingHandler> UDruidsSageThinkingDetailChatItem::GetMessagingHandler() const
{
	return MessagingHandler;
}

void UDruidsSageThinkingDetailChatItem::UpdateFromContentJson(const TSharedPtr<FJsonValue>& ContentJson)
{
	if (!ContentJson.IsValid())
	{
		return;
	}

	SimpleJSON ContentSimpleJson(ContentJson);
	if (ContentSimpleJson["type"].AsString() == TEXT("text"))
	{
		DetailText = ContentSimpleJson["text"].AsString();
		UpdateWidgetStyling();
	}
}

void UDruidsSageThinkingDetailChatItem::InitializeThinkingDetailChatItem(const FString& InDetailText)
{
	DetailText = InDetailText;
	UpdateWidgetStyling();
}

void UDruidsSageThinkingDetailChatItem::SetScrollBoxReference(class UScrollBox* InScrollBox)
{
	SetupMessagingHandler(InScrollBox);
}

void UDruidsSageThinkingDetailChatItem::SetupMessagingHandler(class UScrollBox* ScrollBox)
{
	if (ScrollBox)
	{
		MessagingHandler = NewObject<UDruidsSageMessagingHandler>(this);
		if (MessagingHandler.IsValid())
		{
			MessagingHandler->ScrollBoxReference = ScrollBox;
		}
	}
}

void UDruidsSageThinkingDetailChatItem::UpdateWidgetStyling()
{
	if (DetailWidget)
	{
		DetailWidget->SetNewText(FText::FromString(DetailText));
	}
}

FString UDruidsSageThinkingDetailChatItem::GetPlainText() const
{
	if (DetailWidget)
	{
		return DetailWidget->GetPlainText();
	}
	return DetailText;
}
