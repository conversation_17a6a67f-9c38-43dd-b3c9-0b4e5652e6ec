#include "DruidsSageSuggestedPrompts.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Misc/DateTime.h"
#include "Dom/JsonObject.h"
#include "Engine/Engine.h"
#include "Math/UnrealMathUtility.h"

void DruidsSageSuggestedPrompts::ProcessFreshConversationPrompts(const TArray<TSharedPtr<FJsonValue>>& FreshConversationArray)
{
	// Process each prompt in the fresh conversation array
	for (const TSharedPtr<FJsonValue>& PromptValue : FreshConversationArray)
	{
		const TSharedPtr<FJsonObject>* PromptObject = nullptr;
		if (PromptValue.IsValid() && PromptValue->TryGetObject(PromptObject) && PromptObject)
		{
			FString Summary;
			FString Prompt;
			
			(*PromptObject)->TryGetStringField(TEXT("summary"), Summary);
			(*PromptObject)->TryGetStringField(TEXT("prompt"), Prompt);
			
			// Only add if both summary and prompt are present
			if (!Summary.IsEmpty() && !Prompt.IsEmpty())
			{
				AddSuggestedPrompt(Summary, Prompt);
			}
		}
	}
}

TArray<FSuggestedPromptEntry> DruidsSageSuggestedPrompts::GetSuggestedPrompts() const
{
	return LoadSuggestedPrompts();
}

void DruidsSageSuggestedPrompts::MarkPromptAsUsed(const FString& Summary, const FString& Prompt)
{
	// Load existing entries
	TArray<FSuggestedPromptEntry> PromptEntries = LoadSuggestedPrompts();

	// Remove the used prompt
	if (RemovePrompt(PromptEntries, Summary, Prompt))
	{
		// Save the updated list
		SaveSuggestedPrompts(PromptEntries);
	}
}

FString DruidsSageSuggestedPrompts::GetSuggestedPromptsPath()
{
	return FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("DruidsSage"), TEXT("SuggestedPrompts.md"));
}

void DruidsSageSuggestedPrompts::EnsurePromptsDirectoryExists()
{
	FString PromptsDir = FPaths::GetPath(GetSuggestedPromptsPath());
	IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();

	if (!PlatformFile.DirectoryExists(*PromptsDir))
	{
		PlatformFile.CreateDirectoryTree(*PromptsDir);
	}
}

TArray<FSuggestedPromptEntry> DruidsSageSuggestedPrompts::LoadSuggestedPrompts()
{
	TArray<FSuggestedPromptEntry> PromptEntries;
	FString PromptsPath = GetSuggestedPromptsPath();
	FString PromptsContent;

	if (FFileHelper::LoadFileToString(PromptsContent, *PromptsPath))
	{
		// Parse the markdown content to extract entries
		TArray<FString> Lines;
		PromptsContent.ParseIntoArrayLines(Lines);

		FString CurrentSummary;
		FString CurrentPrompt;
		bool bParsingPrompt = false;

		for (const FString& Line : Lines)
		{
			FString TrimmedLine = Line.TrimStartAndEnd();

			// Skip empty lines and markdown headers
			if (TrimmedLine.IsEmpty() || TrimmedLine.StartsWith(TEXT("###")) || TrimmedLine.StartsWith(TEXT("---")))
			{
				continue;
			}

			// Check for summary line (starts with "**Summary:**")
			if (TrimmedLine.StartsWith(TEXT("**Summary:**")))
			{
				// Save previous entry if we have one
				if (!CurrentSummary.IsEmpty() || !CurrentPrompt.IsEmpty())
				{
					PromptEntries.Add(FSuggestedPromptEntry(CurrentSummary, CurrentPrompt));
				}

				// Extract summary text
				CurrentSummary = TrimmedLine.Mid(12).TrimStartAndEnd(); // Remove "**Summary:**"
				CurrentPrompt.Empty();
				bParsingPrompt = false;
			}
			// Check for prompt line (starts with "**Prompt:**")
			else if (TrimmedLine.StartsWith(TEXT("**Prompt:**")))
			{
				CurrentPrompt = TrimmedLine.Mid(11).TrimStartAndEnd(); // Remove "**Prompt:**"
				bParsingPrompt = false; // Don't continue parsing after the prompt line
			}
		}

		// Save the last entry
		if (!CurrentSummary.IsEmpty() || !CurrentPrompt.IsEmpty())
		{
			PromptEntries.Add(FSuggestedPromptEntry(CurrentSummary, CurrentPrompt));
		}

		// Trim to max entries in case the file had more
		TrimPromptsToMaxEntries(PromptEntries);
	}

	return PromptEntries;
}

void DruidsSageSuggestedPrompts::SaveSuggestedPrompts(const TArray<FSuggestedPromptEntry>& PromptEntries)
{
	EnsurePromptsDirectoryExists();

	FString PromptsPath = GetSuggestedPromptsPath();
	FString PromptsContent;

	// Build markdown content
	for (const FSuggestedPromptEntry& Entry : PromptEntries)
	{
		PromptsContent += TEXT("### Suggested Prompt\n");
		PromptsContent += FString::Printf(TEXT("**Summary:** %s\n"), *Entry.Summary);
		PromptsContent += FString::Printf(TEXT("**Prompt:** %s\n"), *Entry.Prompt);
		PromptsContent += TEXT("---\n\n");
	}

	// Save to file
	FFileHelper::SaveStringToFile(PromptsContent, *PromptsPath);
}

void DruidsSageSuggestedPrompts::TrimPromptsToMaxEntries(TArray<FSuggestedPromptEntry>& PromptEntries)
{
	while (PromptEntries.Num() > MaxPromptEntries)
	{
		PromptEntries.RemoveAt(0);
	}
}

void DruidsSageSuggestedPrompts::AddSuggestedPrompt(const FString& Summary, const FString& Prompt)
{
	// Skip empty prompts
	if (Summary.IsEmpty() && Prompt.IsEmpty())
	{
		return;
	}

	// Load existing entries
	TArray<FSuggestedPromptEntry> PromptEntries = LoadSuggestedPrompts();

	// Check if this prompt already exists
	if (PromptExists(PromptEntries, Summary, Prompt))
	{
		return; // Don't add duplicates
	}

	// Add new entry
	FSuggestedPromptEntry NewEntry(Summary, Prompt);
	PromptEntries.Add(NewEntry);

	// Trim to max entries
	TrimPromptsToMaxEntries(PromptEntries);

	// Save to file
	SaveSuggestedPrompts(PromptEntries);
}

bool DruidsSageSuggestedPrompts::PromptExists(const TArray<FSuggestedPromptEntry>& PromptEntries, const FString& Summary, const FString& Prompt)
{
	for (const FSuggestedPromptEntry& Entry : PromptEntries)
	{
		// Check if both summary and prompt match (case-insensitive)
		if (Entry.Summary.Equals(Summary, ESearchCase::IgnoreCase) &&
			Entry.Prompt.Equals(Prompt, ESearchCase::IgnoreCase))
		{
			return true;
		}
	}
	return false;
}

bool DruidsSageSuggestedPrompts::RemovePrompt(TArray<FSuggestedPromptEntry>& PromptEntries, const FString& Summary, const FString& Prompt)
{
	for (int32 i = 0; i < PromptEntries.Num(); ++i)
	{
		const FSuggestedPromptEntry& Entry = PromptEntries[i];

		// Check if both summary and prompt match (case-insensitive)
		if (Entry.Summary.Equals(Summary, ESearchCase::IgnoreCase) &&
			Entry.Prompt.Equals(Prompt, ESearchCase::IgnoreCase))
		{
			PromptEntries.RemoveAt(i);
			return true;
		}
	}
	return false;
}

void DruidsSageSuggestedPrompts::TestSuggestedPrompts()
{
	UE_LOG(LogTemp, Warning, TEXT("Testing SuggestedPrompts functionality..."));

	// Create a test instance
	DruidsSageSuggestedPrompts TestHandler;

	// Test adding some prompts
	AddSuggestedPrompt(TEXT("Create a Blueprint"), TEXT("Create a new Blueprint class for a player character"));
	AddSuggestedPrompt(TEXT("Add Animation"), TEXT("Add animation support to the character Blueprint"));
	AddSuggestedPrompt(TEXT("Create UI"), TEXT("Create a user interface for the main menu"));

	// Test duplicate prevention
	AddSuggestedPrompt(TEXT("Create a Blueprint"), TEXT("Create a new Blueprint class for a player character")); // Should be ignored

	// Get and log the prompts
	TArray<FSuggestedPromptEntry> Prompts = TestHandler.GetSuggestedPrompts();
	UE_LOG(LogTemp, Warning, TEXT("Found %d suggested prompts:"), Prompts.Num());

	for (int32 i = 0; i < Prompts.Num(); ++i)
	{
		UE_LOG(LogTemp, Warning, TEXT("  %d. %s: %s"), i + 1, *Prompts[i].Summary, *Prompts[i].Prompt);
	}

	// Test marking one as used
	if (Prompts.Num() > 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("Marking first prompt as used..."));
		TestHandler.MarkPromptAsUsed(Prompts[0].Summary, Prompts[0].Prompt);

		// Check the updated list
		TArray<FSuggestedPromptEntry> UpdatedPrompts = TestHandler.GetSuggestedPrompts();
		UE_LOG(LogTemp, Warning, TEXT("After marking as used, found %d suggested prompts:"), UpdatedPrompts.Num());

		for (int32 i = 0; i < UpdatedPrompts.Num(); ++i)
		{
			UE_LOG(LogTemp, Warning, TEXT("  %d. %s: %s"), i + 1, *UpdatedPrompts[i].Summary, *UpdatedPrompts[i].Prompt);
		}
	}

	// Test the random prompts functionality
	UE_LOG(LogTemp, Warning, TEXT("Testing random prompts selection..."));
	TArray<FSuggestedPromptEntry> RandomPrompts;
	GetRandomPrompts(RandomPrompts);
	UE_LOG(LogTemp, Warning, TEXT("GetRandomPrompts returned %d prompts:"), RandomPrompts.Num());
	for (int32 i = 0; i < RandomPrompts.Num(); ++i)
	{
		UE_LOG(LogTemp, Warning, TEXT("  Random %d. %s: %s"), i + 1, *RandomPrompts[i].Summary, *RandomPrompts[i].Prompt);
	}

	UE_LOG(LogTemp, Warning, TEXT("SuggestedPrompts test completed."));
}

void DruidsSageSuggestedPrompts::CleanupSuggestedPromptsFile()
{
	UE_LOG(LogTemp, Warning, TEXT("Cleaning up SuggestedPrompts file..."));

	// Load existing entries (this will parse with the fixed logic)
	TArray<FSuggestedPromptEntry> PromptEntries = LoadSuggestedPrompts();

	UE_LOG(LogTemp, Warning, TEXT("Loaded %d entries for cleanup"), PromptEntries.Num());

	// Remove any duplicates that might exist
	for (int32 i = PromptEntries.Num() - 1; i >= 0; --i)
	{
		for (int32 j = i - 1; j >= 0; --j)
		{
			if (PromptEntries[i].Summary.Equals(PromptEntries[j].Summary, ESearchCase::IgnoreCase) &&
				PromptEntries[i].Prompt.Equals(PromptEntries[j].Prompt, ESearchCase::IgnoreCase))
			{
				UE_LOG(LogTemp, Warning, TEXT("Removing duplicate: %s"), *PromptEntries[i].Summary);
				PromptEntries.RemoveAt(i);
				break;
			}
		}
	}

	// Trim to max entries
	TrimPromptsToMaxEntries(PromptEntries);

	// Save the cleaned entries (this will overwrite the file with clean format)
	SaveSuggestedPrompts(PromptEntries);

	UE_LOG(LogTemp, Warning, TEXT("Cleanup completed. %d entries remain."), PromptEntries.Num());
}

void DruidsSageSuggestedPrompts::GetRandomPrompts(TArray<FSuggestedPromptEntry>& OutPrompts)
{
	OutPrompts.Empty();

	// Load all available prompts
	TArray<FSuggestedPromptEntry> AllPrompts = LoadSuggestedPrompts();

	if (AllPrompts.Num() == 0)
	{
		return; // No prompts available
	}

	// If we have 1 or 2 prompts, just return them all
	if (AllPrompts.Num() <= 2)
	{
		OutPrompts = AllPrompts;
		return;
	}

	// For more than 2 prompts, select 2 random ones
	TArray<int32> SelectedIndices;

	// Select first random index
	int32 FirstIndex = FMath::RandRange(0, AllPrompts.Num() - 1);
	SelectedIndices.Add(FirstIndex);
	OutPrompts.Add(AllPrompts[FirstIndex]);

	// Select second random index (different from first)
	int32 SecondIndex;
	do
	{
		SecondIndex = FMath::RandRange(0, AllPrompts.Num() - 1);
	}
	while (SecondIndex == FirstIndex);

	SelectedIndices.Add(SecondIndex);
	OutPrompts.Add(AllPrompts[SecondIndex]);
}
