#pragma once

#include "CoreMinimal.h"
#include "ISuggestedPromptsHandler.h"
#include "Misc/DateTime.h"

/**
 * Structure to hold a single suggested prompt entry
 */
struct SAGECORE_API FSuggestedPromptEntry
{
	FString Summary;
	FString Prompt;
	FDateTime Timestamp;

	FSuggestedPromptEntry()
		: Timestamp(FDateTime::Now())
	{
	}

	FSuggestedPromptEntry(const FString& InSummary, const FString& InPrompt)
		: Summary(InSummary)
		, Prompt(InPrompt)
		, Timestamp(FDateTime::Now())
	{
	}
};

/**
 * Handles storage and management of suggested prompts to a markdown file
 * Maintains the last 10 suggested prompts in /Saved/DruidsSage/SuggestedPrompts.md
 */
class SAGECORE_API DruidsSageSuggestedPrompts : public ISuggestedPromptsHandler
{
public:
	// ISuggestedPromptsHandler interface
	virtual void ProcessFreshConversationPrompts(const TArray<TSharedPtr<FJsonValue>>& FreshConversationArray) override;
	virtual TArray<FSuggestedPromptEntry> GetSuggestedPrompts() const override;
	virtual void MarkPromptAsUsed(const FString& Summary, const FString& Prompt) override;

private:
	/**
	 * Get the path to the suggested prompts file
	 */
	static FString GetSuggestedPromptsPath();

	/**
	 * Ensure the suggested prompts directory exists
	 */
	static void EnsurePromptsDirectoryExists();

	/**
	 * Load existing suggested prompt entries from file
	 * @return Array of suggested prompt entries loaded from file
	 */
	static TArray<FSuggestedPromptEntry> LoadSuggestedPrompts();

	/**
	 * Save suggested prompt entries to file
	 * @param PromptEntries Array of suggested prompt entries to save
	 */
	static void SaveSuggestedPrompts(const TArray<FSuggestedPromptEntry>& PromptEntries);

	/**
	 * Trim the prompts to maintain only the last 10 entries
	 * @param PromptEntries Array to trim (modified in place)
	 */
	static void TrimPromptsToMaxEntries(TArray<FSuggestedPromptEntry>& PromptEntries);

	/**
	 * Add a new suggested prompt entry (only if it doesn't already exist)
	 * @param Summary The summary text of the prompt
	 * @param Prompt The full prompt text
	 */
	static void AddSuggestedPrompt(const FString& Summary, const FString& Prompt);

	/**
	 * Check if a prompt already exists in the list
	 * @param PromptEntries Array of existing prompt entries
	 * @param Summary The summary to check for
	 * @param Prompt The prompt to check for
	 * @return True if the prompt already exists
	 */
	static bool PromptExists(const TArray<FSuggestedPromptEntry>& PromptEntries, const FString& Summary, const FString& Prompt);

	/**
	 * Remove a specific prompt from the list
	 * @param PromptEntries Array to remove from (modified in place)
	 * @param Summary The summary of the prompt to remove
	 * @param Prompt The prompt text to remove
	 * @return True if a prompt was removed
	 */
	static bool RemovePrompt(TArray<FSuggestedPromptEntry>& PromptEntries, const FString& Summary, const FString& Prompt);

	/**
	 * Maximum number of entries to keep in the suggested prompts
	 */
	static constexpr int32 MaxPromptEntries = 10;

public:
	/**
	 * Test function to verify the suggested prompts functionality
	 * This can be called from Blueprint or C++ to test the system
	 */
	static void TestSuggestedPrompts();

	/**
	 * Clean up the suggested prompts file by reloading and resaving it
	 * This can help fix any parsing issues in existing files
	 */
	static void CleanupSuggestedPromptsFile();

	/**
	 * Get up to 2 random suggested prompts for display
	 * @param OutPrompts Array to fill with random prompts (will contain 0-2 entries)
	 */
	static void GetRandomPrompts(TArray<FSuggestedPromptEntry>& OutPrompts);
};
