#pragma once

#include "CoreMinimal.h"
#include "Dom/JsonValue.h"

// Forward declaration
struct FSuggestedPromptEntry;

/**
 * Interface for handling suggested prompts
 * This allows UI components to work with suggested prompts without depending on the concrete implementation
 */
class SAGECORE_API ISuggestedPromptsHandler
{
public:
	virtual ~ISuggestedPromptsHandler() = default;

	/**
	 * Process fresh conversation suggested prompts from JSON
	 * @param FreshConversationArray Array of JSON values containing suggested prompts
	 */
	virtual void ProcessFreshConversationPrompts(const TArray<TSharedPtr<FJsonValue>>& FreshConversationArray) = 0;

	/**
	 * Get all stored suggested prompts as an array of entries
	 * @return Array of suggested prompt entries
	 */
	virtual TArray<struct FSuggestedPromptEntry> GetSuggestedPrompts() const = 0;

	/**
	 * Mark a suggested prompt as used and remove it from the list
	 * @param Summary The summary of the prompt that was used
	 * @param Prompt The full prompt text that was used
	 */
	virtual void MarkPromptAsUsed(const FString& Summary, const FString& Prompt) = 0;
};
